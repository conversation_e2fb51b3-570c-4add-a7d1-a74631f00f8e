using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace Wlia.Pages
{
    public class IndexModel : PageModel
    {
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(ILogger<IndexModel> logger)
        {
            _logger = logger;
        }

        public async Task<IActionResult> OnGetAsync(string? trigger)
        {
            System.Diagnostics.Debug.WriteLine("BUTTON PRESSED");
            if (trigger == "true")
            {
                using var httpClient = new HttpClient();
                var response = await httpClient.GetAsync("https://jerry.dieboldnixdorf.com/rest/api/2/issue/PLAYHWRMV2-94");
                System.Diagnostics.Debug.WriteLine(response);
                // Optional: handle the response
                if (response.IsSuccessStatusCode)
                {

                    var responseData = await response.Content.ReadAsStringAsync();
                    Console.WriteLine("Response from API:");
                    Console.WriteLine(responseData);
                    System.Diagnostics.Debug.WriteLine("HII");

                    // Do something with the response
                }

                else
                {
                    Console.WriteLine($"Request failed with status code: {response.StatusCode}");
                }

            }

            return Page();
        }

    }
}
